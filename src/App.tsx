// React core
import { useState, useEffect, useCallback } from "react";
// Third-party library imports
// Services
import { AppService } from "./services/AppService";
import { ConversationStorage } from "./services/ConversationStorage";
// Components
import { Header } from "./components/shared/Header";
import { <PERSON><PERSON> } from "./components/views/Cookie/Cookie";
import { Welcome } from "./components/views/Welcome/Welcome";
import { MainMenu } from "./components/views/MainMenu/MainMenu";
import { GameRules } from "./components/views/GameRules/GameRules";
import { SimpleVoiceChat } from "./components/SimpleVoiceChat";
import { GameLive } from "./components/views/GameLive/GameLive";
import { GameHint } from "./components/views/GameHint/GameHint";
import { GameExit } from "./components/views/GameExit/GameExit";
import { GameEndScreen } from "./components/GameEndScreen";
import { DebugPanel } from "./components/DebugPanel";
// Utils & Constants & Helpers
import { GameStep, type GameStepType, GAME_MESSAGES } from "./utils/gameUtils";
import { playAudioWithFallback } from "./utils/audioUtils";
import { checkCookieConsent, saveCookieConsent } from "./utils/cookieUtils";
// Styles
import "./App.scss";

function App() {
  // Step management - Initialize based on cookie consent
  const [currentStep, setCurrentStep] = useState<GameStepType>(() => {
    return checkCookieConsent() ? GameStep.WELCOME : GameStep.COOKIE_BANNER;
  });
  const [previousView, setPreviousView] = useState<GameStepType | null>(null);
  const [showRulesPopup, setShowRulesPopup] = useState<boolean>(false);

  // Game state management
  const [aiLoading, setAiLoading] = useState<boolean>(false);
  const [generatedCharacter, setGeneratedCharacter] = useState<string>("");
  const [gameStarted, setGameStarted] = useState<boolean>(false);
  const [initialMessage, setInitialMessage] = useState<string>("");
  const [gameWon, setGameWon] = useState<boolean>(false);
  const [, setCharacterError] = useState<string>("");

  // Service instances (singletons)
  const appService = AppService.getInstance();
  const conversationStorage = ConversationStorage.getInstance();

  /**
   * Initialize app by clearing previous conversations
   * This ensures a fresh start for each session
   */
  useEffect(() => {
    conversationStorage.clearAllConversations();
  }, [conversationStorage]);

  /**
   * Configure audio playback callback
   * Sets up automatic audio playback with fallback handling
   */
  useEffect(() => {
    appService.setAudioCallback((audioUrl: string) => {
      // Small delay to ensure proper audio context initialization
      setTimeout(() => {
        const audioFinishedCallback = appService.getAudioFinishedCallback();
        const audioStartedCallback = appService.getAudioStartedCallback();

        playAudioWithFallback(
          audioUrl,
          audioFinishedCallback, // onEnded
          audioStartedCallback, // onStarted
          (error: string) => {
            // onError
            console.error("❌ Error reproduciendo audio:", error);
            if (audioFinishedCallback) {
              setTimeout(() => {
                audioFinishedCallback();
              }, 1000);
            }
          }
        );
      }, 100);
    });
  }, [appService]);

  /**
   * Extract response text from API response object
   * Handles multiple possible response field names
   */
  const extractResponseText = useCallback(
    (response: any, fallback = "Respuesta no encontrada") => {
      return (
        response.response ||
        response.output ||
        response.result ||
        response.text ||
        response.content ||
        fallback
      );
    },
    []
  );

  /*********************************************************************************************
   * Step navigation functions
   *********************************************************************************************/

  const handleAcceptCookies = useCallback(() => {
    saveCookieConsent();
    setCurrentStep(GameStep.WELCOME);
  }, []);

  const handleDeclineCookies = useCallback(() => {
    setCurrentStep(GameStep.WELCOME);
  }, []);

  const handleAcceptAndPlay = useCallback(() => {
    setCurrentStep(GameStep.MAIN_MENU);
  }, []);

  const navigateBack = useCallback(() => {
    if (previousView) {
      setCurrentStep(previousView);
      setPreviousView(null);
    } else {
      setCurrentStep(GameStep.MAIN_MENU);
    }
  }, [previousView]);

  const handleShowRules = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_RULES);
  }, [currentStep]);

  const handleShowGameLive = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_LIVE);
  }, [currentStep]);

  const handleShowGameHint = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_HINT);
  }, [currentStep]);

  const handleShowGameExit = useCallback(() => {
    setPreviousView(currentStep);
    setCurrentStep(GameStep.GAME_EXIT);
  }, [currentStep]);

  /*********************************************************************************************
   * Game logic functions
   *********************************************************************************************/

  /**
   * Main game initialization function
   * Combines character generation and game start in a single flow
   */
  const handleStartGameDirectly = useCallback(async () => {
    setAiLoading(true);
    setCharacterError("");

    try {
      // Step 1: Generate character
      const characterResponse = await appService.generateWithGenCharBot(
        GAME_MESSAGES.GENERATE_CHARACTER
      );

      const characterText = extractResponseText(characterResponse);
      if (!characterText || characterText === "Respuesta no encontrada") {
        throw new Error("No se pudo generar el personaje");
      }

      setGeneratedCharacter(characterText);

      // Step 2: Start game immediately
      setGameStarted(true);

      const gameResponse = await appService.generateWithIaVsPlayer(
        GAME_MESSAGES.INITIAL_GAME_QUERY,
        characterText
      );

      const responseText = extractResponseText(gameResponse);
      setInitialMessage(responseText);
    } catch (error) {
      console.error("❌ Error en inicio directo del juego:", error);
      setCharacterError(
        "Error al generar personaje o iniciar juego. Inténtalo de nuevo."
      );
      setGameStarted(false);
      setGeneratedCharacter("");
    } finally {
      setAiLoading(false);
    }
  }, [appService, extractResponseText]);

  /**
   * Handle start game from main menu
   * Transitions to game playing step and starts the game
   */
  const handleStartGame = useCallback(() => {
    setCurrentStep(GameStep.GAME_PLAYING);
    handleStartGameDirectly();
  }, [handleStartGameDirectly]);

  /**
   * Handle game end
   * Transitions to game end step and stores the result
   */
  const handleGameEnd = useCallback((won: boolean) => {
    setGameWon(won);
    setCurrentStep(GameStep.GAME_END);
  }, []);

  /**
   * Reset game to initial state
   * Clears all game data and conversation history
   */
  const handleResetGame = useCallback(() => {
    setCurrentStep(GameStep.MAIN_MENU);
    setGeneratedCharacter("");
    setGameStarted(false);
    setInitialMessage("");
    setGameWon(false);
    setCharacterError("");
    setShowRulesPopup(false);
    conversationStorage.clearAllConversations();
  }, [conversationStorage]);

  /*********************************************************************************************
   * Render step content based on current step
   *********************************************************************************************/

  const renderStepContent = () => {
    switch (currentStep) {
      case GameStep.COOKIE_BANNER:
        return (
          <Cookie
            onAccept={handleAcceptCookies}
            onDecline={handleDeclineCookies}
          />
        );

      case GameStep.WELCOME:
        return (
          <Welcome
            handleAcceptAndPlay={handleAcceptAndPlay}
            aiLoading={aiLoading}
          />
        );

      case GameStep.MAIN_MENU:
        return (
          <MainMenu
            onStartGame={handleStartGame}
            onShowRules={handleShowRules}
            aiLoading={aiLoading}
          />
        );

      case GameStep.GAME_RULES:
        return <GameRules />;

      case GameStep.GAME_PLAYING:
        return (
          <div className="card">
            <div className="game-container">
              <h3 className="game-title">
                🎯 Juego de Adivinanza de Personajes
              </h3>

              <SimpleVoiceChat
                generatedCharacter={generatedCharacter}
                isGameStarted={gameStarted}
                initialMessage={initialMessage}
                onGameEnd={handleGameEnd}
              />

              {(generatedCharacter || gameStarted) && (
                <>
                  <div className="game-navigation">
                    <button onClick={handleShowGameLive} className="nav-button">
                      ❤️ Vidas
                    </button>
                    <button onClick={handleShowGameHint} className="nav-button">
                      💡 Pistas
                    </button>
                    <button onClick={handleShowGameExit} className="nav-button">
                      🚪 Salir
                    </button>
                  </div>
                  <div className="reset-section">
                    <button onClick={handleResetGame} className="reset-button">
                      🔄 Reiniciar Juego
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        );

      case GameStep.GAME_LIVE:
        return <GameLive />;

      case GameStep.GAME_HINT:
        return <GameHint />;

      case GameStep.GAME_EXIT:
        return (
          <GameExit
            onConfirmExit={handleResetGame}
            onCancelExit={navigateBack}
          />
        );

      case GameStep.GAME_END:
        return (
          <GameEndScreen
            gameWon={gameWon}
            onClose={handleResetGame}
            onNewGame={handleResetGame}
          />
        );

      default:
        return null;
    }
  };

  return (
    <>
      {currentStep !== GameStep.COOKIE_BANNER &&
        currentStep !== GameStep.WELCOME && (
          <Header
            currentStep={currentStep}
            onBackToMain={navigateBack}
            showBackButton={
              currentStep === GameStep.GAME_RULES ||
              currentStep === GameStep.GAME_HINT ||
              currentStep === GameStep.GAME_LIVE ||
              currentStep === GameStep.GAME_EXIT
            }
          />
        )}

      {renderStepContent()}

      {/* Debug Panel - Only shown in development */}
      <DebugPanel
        currentStep={currentStep}
        showRulesPopup={showRulesPopup}
        aiLoading={aiLoading}
        generatedCharacter={generatedCharacter}
        gameStarted={gameStarted}
        initialMessage={initialMessage}
        gameWon={gameWon}
      />
    </>
  );
}

export default App;
